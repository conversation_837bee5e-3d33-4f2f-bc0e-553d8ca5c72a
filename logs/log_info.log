2025-08-04 17:35:49.235 [main] INFO  com.erdos.coal.Application - Starting Application on lhw with PID 14752 (D:\work\javaweb_v2\coal-project\coal-park\target\classes started by yxhyn in D:\work\javaweb_v2\coal-project)
2025-08-04 17:35:49.239 [main] INFO  com.erdos.coal.Application - The following profiles are active: dev,tra-dev
2025-08-04 17:36:02.123 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data repositories in DEFAULT mode.
2025-08-04 17:36:02.229 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 98ms. Found 0 repository interfaces.
2025-08-04 17:36:02.581 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'shiroConfig' of type [com.erdos.coal.core.security.shiro.config.ShiroConfig$$EnhancerBySpringCGLIB$$f0354a94] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 17:36:02.670 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'spring.data.mongodb-org.springframework.boot.autoconfigure.mongo.MongoProperties' of type [org.springframework.boot.autoconfigure.mongo.MongoProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 17:36:02.676 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration' of type [org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration$$EnhancerBySpringCGLIB$$80b322f3] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 17:36:03.686 [main] INFO  org.mongodb.driver.cluster - Cluster created with settings {hosts=[127.0.0.1:27017], mode=SINGLE, requiredClusterType=UNKNOWN, serverSelectionTimeout='30000 ms', maxWaitQueueSize=500}
2025-08-04 17:36:03.772 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'mongo' of type [com.mongodb.MongoClient] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 17:36:03.774 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'morphiaFactory' of type [com.erdos.coal.core.config.MorphiaFactory$$EnhancerBySpringCGLIB$$8871ba58] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 17:36:03.930 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'dsForCoal' of type [dev.morphia.DatastoreImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 17:36:03.935 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.data.mongo.MongoDataAutoConfiguration' of type [org.springframework.boot.autoconfigure.data.mongo.MongoDataAutoConfiguration$$EnhancerBySpringCGLIB$$af71a5d5] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 17:36:03.947 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'mongoDbFactory' of type [org.springframework.data.mongodb.core.SimpleMongoDbFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 17:36:03.949 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'mongoConfig' of type [com.erdos.coal.core.config.MongoConfig$$EnhancerBySpringCGLIB$$60ee7e3c] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 17:36:03.954 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.boot.autoconfigure.data.mongo.MongoDataConfiguration' of type [org.springframework.boot.autoconfigure.data.mongo.MongoDataConfiguration$$EnhancerBySpringCGLIB$$70a71424] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 17:36:04.091 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'mongoCustomConversions' of type [org.springframework.data.mongodb.core.convert.MongoCustomConversions] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 17:36:04.157 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'mongoMappingContext' of type [org.springframework.data.mongodb.core.mapping.MongoMappingContext] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 17:36:04.209 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'mappingMongoConverter' of type [org.springframework.data.mongodb.core.convert.MappingMongoConverter] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 17:36:04.254 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'mongoTemplate' of type [org.springframework.data.mongodb.core.MongoTemplate] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 17:36:04.269 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'sysUnitAccountService' of type [com.erdos.coal.park.web.sys.service.impl.SysUnitAccountServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 17:36:04.270 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'sysUnitService' of type [com.erdos.coal.park.web.sys.service.impl.SysUnitServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 17:36:04.271 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'bizUserLoginImpl' of type [com.erdos.coal.security.impl.BizUserLoginImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 17:36:04.281 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'smsResultDao' of type [com.erdos.coal.park.api.manage.dao.impl.SMSResultDao] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 17:36:04.281 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'smsService' of type [com.erdos.coal.park.api.manage.service.impl.SMSServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 17:36:04.288 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'userTokenRecordService' of type [com.erdos.coal.park.api.manage.service.impl.UserTokenRecordServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 17:36:04.290 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'customerOpenidService' of type [com.erdos.coal.park.api.customer.service.impl.CustomerOpenidServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 17:36:04.290 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'customerService' of type [com.erdos.coal.park.api.customer.service.impl.CustomerUserServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 17:36:04.291 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'customerUserLoginImpl' of type [com.erdos.coal.security.impl.CustomerUserLoginImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 17:36:04.304 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'coalConfig' of type [com.erdos.coal.config.CoalConfig$$EnhancerBySpringCGLIB$$f942e948] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 17:36:04.305 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'fileInfoDao' of type [com.erdos.coal.park.api.driver.dao.impl.FileInfoDaoImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 17:36:04.314 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'carInfoService' of type [com.erdos.coal.park.api.manage.service.impl.CarInfoServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 17:36:04.317 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'httpClientConfig' of type [com.erdos.coal.config.HttpClientConfig$$EnhancerBySpringCGLIB$$31ac2ce4] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 17:36:04.378 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'httpClientConnectionManager' of type [org.apache.http.impl.conn.PoolingHttpClientConnectionManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 17:36:04.385 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'httpClientBuilder' of type [org.apache.http.impl.client.HttpClientBuilder] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 17:36:04.421 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'getCloseableHttpClient' of type [org.apache.http.impl.client.InternalHttpClient] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 17:36:04.424 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'builder' of type [org.apache.http.client.config.RequestConfig$Builder] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 17:36:04.425 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'getRequestConfig' of type [org.apache.http.client.config.RequestConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 17:36:04.425 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'httpAPIService' of type [com.erdos.coal.httpclient.service.impl.HttpAPIService] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 17:36:04.427 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'carDao' of type [com.erdos.coal.park.api.driver.dao.impl.CarDaoImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 17:36:04.429 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'driverToCarDao' of type [com.erdos.coal.park.api.driver.dao.impl.DriverToCarDaoImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 17:36:04.430 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'photoFileService' of type [com.erdos.coal.park.api.manage.service.impl.PhotoFileServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 17:36:04.437 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'unitInfoService' of type [com.erdos.coal.park.api.business.service.impl.UnitInfoServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 17:36:04.437 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'contractService' of type [com.erdos.coal.park.api.business.service.impl.ContractServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 17:36:04.437 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'wechatDriverInfoService' of type [com.erdos.coal.park.api.driver.service.impl.WechatDriverInfoServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 17:36:04.437 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'sysSwitchService' of type [com.erdos.coal.park.web.sys.service.impl.SysSwitchServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 17:36:04.439 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'driverOpenidService' of type [com.erdos.coal.park.api.driver.service.impl.DriverOpenidServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 17:36:04.439 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'driverInfoService' of type [com.erdos.coal.park.api.driver.service.impl.DriverInfoServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 17:36:04.440 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'driverUserLoginImpl' of type [com.erdos.coal.security.impl.DriverUserLoginImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 17:36:04.443 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'sysUserService' of type [com.erdos.coal.park.web.sys.service.impl.SysUserServiceImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 17:36:04.443 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'webUserLoginImpl' of type [com.erdos.coal.security.impl.WebUserLoginImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 17:36:04.444 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'wechatUserLoginImpl' of type [com.erdos.coal.security.impl.WechatUserLoginImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 17:36:04.446 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'cacheConfig' of type [com.erdos.coal.core.config.CacheConfig$$EnhancerBySpringCGLIB$$4973d60a] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 17:36:04.454 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'caffeineCacheManager' of type [org.springframework.cache.caffeine.CaffeineCacheManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 17:36:04.454 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'shiroUserSecurity' of type [com.erdos.coal.security.ShiroUserSecurity] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 17:36:04.454 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'shiroRealmService' of type [com.erdos.coal.core.security.shiro.service.ShiroRealmService] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 17:36:04.510 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'getManager' of type [org.apache.shiro.web.mgt.DefaultWebSecurityManager] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 17:36:04.610 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'authorizationAttributeSourceAdvisor' of type [org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 17:36:04.684 [main] INFO  o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration' of type [org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration$$EnhancerBySpringCGLIB$$29060195] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 17:36:05.561 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-08-04 17:36:05.588 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-04 17:36:05.603 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-04 17:36:05.603 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.16]
2025-08-04 17:36:05.618 [main] INFO  org.apache.catalina.core.AprLifecycleListener - The APR based Apache Tomcat Native library which allows optimal performance in production environments was not found on the java.library.path: [D:\application\jvms_v2.1.6_386\store\jdk1.8.0_271\bin;C:\WINDOWS\Sun\Java\bin;C:\WINDOWS\system32;C:\WINDOWS;C:\Program Files\Common Files\Oracle\Java\javapath;D:\application\VMware\bin\;C:\Windows\System32\HWAudioDriver;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\application\jvms_v2.1.6_386\store\jdk1.8.0_271\bin;C:\Program Files\Bandizip\;D:\develop\Java\Git\cmd;D:\develop\Java\Redis-x64-********;D:\develop\Java\apache-maven-3.6.3\bin;D:\develop\Java\gradle-7.4.1\bin;D:\application\微信web开发者工具\dll;D:\application\TortoiseSVN\bin;D:\work\devtools\mysql-5.7.40\bin;D:\develop\frontend\nvm;D:\develop\frontend\nodejs;C:\Program Files\dotnet\;D:\application\jvms_v2.1.6_386;D:\develop\Java\mongodb-4.2.20\bin;D:\application\jvms_v2.1.6_386\store\jdk1.8.0_271\jre\bin;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;D:\develop\frontend\nodejs\node_global;D:\develop\frontend\nodejs\node_global;C:\Program Files\Docker\Docker\resources\bin;D:\application\anaconda3;D:\application\anaconda3\Scripts;D:\application\anaconda3\Library\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\JetBrains\Toolbox\scripts;D:\application\Microsoft VS Code\bin;C:\Users\<USER>\.dotnet\tools;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\application\cursor\resources\app\bin;C:\Users\<USER>\AppData\Local\Programs\Ollama;.]
2025-08-04 17:36:05.812 [main] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-04 17:36:05.812 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 16523 ms
2025-08-04 17:36:05.824 [cluster-ClusterId{value='68907f0323450739a02ed231', description='null'}-127.0.0.1:27017] INFO  org.mongodb.driver.cluster - Exception in monitor thread while connecting to server 127.0.0.1:27017
com.mongodb.MongoSocketOpenException: Exception opening socket
	at com.mongodb.internal.connection.SocketStream.open(SocketStream.java:67)
	at com.mongodb.internal.connection.InternalStreamConnection.open(InternalStreamConnection.java:126)
	at com.mongodb.internal.connection.DefaultServerMonitor$ServerMonitorRunnable.run(DefaultServerMonitor.java:117)
	at java.lang.Thread.run(Thread.java:748)
Caused by: java.net.ConnectException: Connection refused: connect
	at java.net.DualStackPlainSocketImpl.waitForConnect(Native Method)
	at java.net.DualStackPlainSocketImpl.socketConnect(DualStackPlainSocketImpl.java:81)
	at java.net.AbstractPlainSocketImpl.doConnect(AbstractPlainSocketImpl.java:476)
	at java.net.AbstractPlainSocketImpl.connectToAddress(AbstractPlainSocketImpl.java:218)
	at java.net.AbstractPlainSocketImpl.connect(AbstractPlainSocketImpl.java:200)
	at java.net.PlainSocketImpl.connect(PlainSocketImpl.java:162)
	at java.net.SocksSocketImpl.connect(SocksSocketImpl.java:394)
	at java.net.Socket.connect(Socket.java:606)
	at com.mongodb.internal.connection.SocketStreamHelper.initialize(SocketStreamHelper.java:64)
	at com.mongodb.internal.connection.SocketStream.open(SocketStream.java:62)
	... 3 common frames omitted
2025-08-04 17:36:14.482 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskExecutor - Initializing ExecutorService 'busTaskExecutor'
2025-08-04 17:36:14.492 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskExecutor - Initializing ExecutorService 'logTaskExecutor'
2025-08-04 17:36:14.500 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskExecutor - Initializing ExecutorService 'loginTaskExecutor'
2025-08-04 17:36:16.071 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Initializing ExecutorService 'taskScheduler'
2025-08-04 17:36:16.548 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9090 (http)
2025-08-04 17:36:16.549 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-127.0.0.1-9090"]
2025-08-04 17:36:16.549 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-04 17:36:16.549 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.16]
2025-08-04 17:36:16.572 [main] INFO  o.a.c.c.ContainerBase.[Tomcat-1].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-04 17:36:16.572 [main] INFO  org.springframework.web.context.ContextLoader - Root WebApplicationContext: initialization completed in 142 ms
2025-08-04 17:36:16.607 [main] INFO  o.s.b.actuate.endpoint.web.EndpointLinksResolver - Exposing 16 endpoint(s) beneath base path ''
2025-08-04 17:36:16.706 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-127.0.0.1-9090"]
2025-08-04 17:36:16.767 [main] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9090 (http) with context path ''
2025-08-04 17:36:16.790 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-04 17:36:16.797 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8080"]
2025-08-04 17:36:16.797 [main] INFO  org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2025-08-04 17:36:16.808 [main] INFO  org.apache.catalina.util.LifecycleBase - The stop() method was called on component [StandardServer[-1]] after stop() had already been called. The second call will be ignored.
2025-08-04 17:36:16.808 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8080"]
2025-08-04 17:36:16.808 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Destroying ProtocolHandler ["http-nio-8080"]
2025-08-04 17:36:16.814 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-08-04 17:36:16.855 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskScheduler - Shutting down ExecutorService 'taskScheduler'
2025-08-04 17:36:16.856 [main] INFO  o.s.jmx.export.annotation.AnnotationMBeanExporter - Could not unregister MBean [com.github.tobato.fastdfs.domain.conn:name=fdfsConnectionPool,type=FdfsConnectionPool] as said MBean is not registered (perhaps already unregistered by an external process)
2025-08-04 17:36:16.857 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskExecutor - Shutting down ExecutorService 'loginTaskExecutor'
2025-08-04 17:36:16.857 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskExecutor - Shutting down ExecutorService 'logTaskExecutor'
2025-08-04 17:36:16.857 [main] INFO  o.s.scheduling.concurrent.ThreadPoolTaskExecutor - Shutting down ExecutorService 'busTaskExecutor'
