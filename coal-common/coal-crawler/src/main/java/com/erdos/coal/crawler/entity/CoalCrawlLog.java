package com.erdos.coal.crawler.entity;

import com.erdos.coal.core.base.mongo.BaseMongoInfo;
import com.erdos.coal.crawler.enums.IndexType;
import dev.morphia.annotations.Entity;
import dev.morphia.annotations.Field;
import dev.morphia.annotations.Index;
import dev.morphia.annotations.IndexOptions;
import dev.morphia.annotations.Indexes;
import dev.morphia.annotations.Property;

import java.util.Date;

/**
 * 煤炭指数爬取日志实体
 * 记录爬虫执行情况，便于监控和调试
 */
@Entity(value = "t_coal_crawl_log", noClassnameStored = true)
@Indexes(value = {
        @Index(fields = {@Field("startTime")},
               options = @IndexOptions(name = "idx_start_time", background = true)),
        @Index(fields = {@Field("indexType"), @Field("startTime")},
               options = @IndexOptions(name = "idx_type_start_time", background = true)),
        @Index(fields = {@Field("crawlStatus")},
               options = @IndexOptions(name = "idx_crawl_status", background = true))
})
public class CoalCrawlLog extends BaseMongoInfo {
    
    /**
     * 指数类型：SHENHUA/CCTD/CCI
     */
    @Property("indexType")
    private IndexType indexType;
    
    /**
     * 爬取开始时间
     */
    @Property("startTime")
    private Date startTime;

    /**
     * 爬取结束时间
     */
    @Property("endTime")
    private Date endTime;

    /**
     * 爬取状态：0-失败，1-成功
     */
    @Property("crawlStatus")
    private Integer crawlStatus;
    
    /**
     * 目标URL
     */
    @Property("sourceUrl")
    private String sourceUrl;
    
    /**
     * 爬取到的数据条数
     */
    @Property("dataCount")
    private Integer dataCount = 0;
    
    /**
     * 错误信息（如果爬取失败）
     */
    @Property("errorMessage")
    private String errorMessage;
    
    /**
     * 执行耗时（毫秒）
     */
    @Property("duration")
    private Long duration;
    
    /**
     * 爬虫版本或标识
     */
    @Property("crawlerVersion")
    private String crawlerVersion;
    
    /**
     * 详细日志信息
     */
    @Property("logDetail")
    private String logDetail;
    
    // 构造函数
    public CoalCrawlLog() {
        this.startTime = new Date();
        this.crawlStatus = 0; // 默认为进行中
    }

    public CoalCrawlLog(IndexType indexType, String sourceUrl) {
        this();
        this.indexType = indexType;
        this.sourceUrl = sourceUrl;
    }
    
    // Getter和Setter方法
    public IndexType getIndexType() {
        return indexType;
    }
    
    public void setIndexType(IndexType indexType) {
        this.indexType = indexType;
    }
    
    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Integer getCrawlStatus() {
        return crawlStatus;
    }

    public void setCrawlStatus(Integer crawlStatus) {
        this.crawlStatus = crawlStatus;
    }

    public String getSourceUrl() {
        return sourceUrl;
    }

    public void setSourceUrl(String sourceUrl) {
        this.sourceUrl = sourceUrl;
    }
    
    public Integer getDataCount() {
        return dataCount;
    }
    
    public void setDataCount(Integer dataCount) {
        this.dataCount = dataCount;
    }
    
    public String getErrorMessage() {
        return errorMessage;
    }
    
    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }
    
    public Long getDuration() {
        return duration;
    }
    
    public void setDuration(Long duration) {
        this.duration = duration;
    }
    
    public String getCrawlerVersion() {
        return crawlerVersion;
    }
    
    public void setCrawlerVersion(String crawlerVersion) {
        this.crawlerVersion = crawlerVersion;
    }
    
    public String getLogDetail() {
        return logDetail;
    }
    
    public void setLogDetail(String logDetail) {
        this.logDetail = logDetail;
    }
}
